import axios from 'axios';

// Configuration de l'API avec timeout et gestion des erreurs
const API_URL = import.meta.env.VITE_REACT_APP_API_URL || 'https://laravel-api.fly.dev/api';

// Configuration d'axios avec timeout et intercepteurs
const axiosInstance = axios.create({
  baseURL: API_URL,
  timeout: 10000, // 10 secondes de timeout
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  }
});

// Intercepteur pour ajouter le token d'authentification
axiosInstance.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('access_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Intercepteur pour gérer les erreurs de réponse
axiosInstance.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.code === 'ECONNABORTED') {
      console.error('La requête a expiré');
      throw new Error('La connexion au serveur a expiré. Veuillez réessayer.');
    }

    if (!error.response) {
      console.error('Erreur réseau:', error);
      throw new Error('Impossible de se connecter au serveur. Veuillez vérifier votre connexion internet.');
    }

    // Gestion des erreurs HTTP spécifiques
    switch (error.response.status) {
      case 401:
        // Redirection vers la page de connexion si non authentifié
        localStorage.removeItem('access_token');
        window.location.href = '/login';
        throw new Error('Session expirée. Veuillez vous reconnecter.');
      case 403:
        throw new Error('Vous n\'avez pas les permissions nécessaires.');
      case 404:
        throw new Error('La ressource demandée n\'existe pas.');
      case 500:
        throw new Error('Erreur serveur. Veuillez réessayer plus tard.');
      default:
        throw new Error(error.response.data?.message || 'Une erreur est survenue.');
    }
  }
);

// Helper function to map status codes to labels according to API documentation
function getStatusLabel(status) {
  const statusMap = {
    'en_attente': 'En attente',
    'confirmee': 'Confirmée',
    'en_preparation': 'En préparation',
    'expediee': 'Expédiée',
    'livree': 'Livrée',
    'annulee': 'Annulée',
    'remboursee': 'Remboursée',
    'retournee': 'Retournée'
  };

  return statusMap[status] || status || 'En attente';
}

// Fetch orders with pagination and filters
export const fetchOrders = async (params = {}) => {
  try {
    console.log('🔄 Fetching orders with params:', params);
    console.log('🔄 API URL:', API_URL);
    console.log('🔄 Full URL:', `${API_URL}/commandes`);

    const response = await axiosInstance.get('/commandes', { params });

    console.log('✅ Orders API response:', {
      status: response.status,
      dataStructure: {
        hasData: !!response.data,
        dataType: typeof response.data,
        dataKeys: response.data ? Object.keys(response.data) : [],
        dataLength: Array.isArray(response.data?.data) ? response.data.data.length : 'not array'
      }
    });

    return response.data;
  } catch (error) {
    console.error('❌ Error fetching orders:', {
      message: error.message,
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
      url: error.config?.url,
      method: error.config?.method,
      headers: error.config?.headers
    });
    throw error;
  }
};

// Fetch single order by ID
export const fetchOrderById = async (id) => {
  try {
    console.log('🔍 Fetching order by ID:', id);
    console.log('🔍 Full URL:', `${API_URL}/commandes/${id}`);

    // Add query parameter to include related data according to API documentation
    const response = await axiosInstance.get(`/commandes/${id}`, {
      params: {
        with: 'user,produits,paiement,client'
      }
    });

    console.log('✅ Order API response:', {
      status: response.status,
      hasData: !!response.data,
      dataStructure: response.data ? Object.keys(response.data) : []
    });

    // Handle different response structures
    if (response.data && response.data.data) {
      return response.data.data;
    } else if (response.data) {
      return response.data;
    } else {
      throw new Error('Aucune donnée de commande reçue');
    }
  } catch (error) {
    console.error('❌ Error fetching order:', {
      message: error.message,
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
      url: error.config?.url
    });

    // Provide more specific error messages
    if (error.response?.status === 404) {
      throw new Error(`Commande #${id} introuvable`);
    } else if (error.response?.status === 500) {
      throw new Error(`Erreur serveur lors du chargement de la commande #${id}. Veuillez réessayer.`);
    }

    throw error;
  }
};

// Update order status
export const updateOrderStatus = async (orderId, status, notes = '') => {
  try {
    const response = await axiosInstance.patch(`/commandes/${orderId}/status`, {
      status,
      notes,
      send_notification: true
    });
    return response.data.data;
  } catch (error) {
    console.error('Error updating order status:', error);
    throw error;
  }
};

// Cancel order
export const cancelOrder = async (orderId, reason = '') => {
  try {
    const response = await axiosInstance.post(`/commandes/${orderId}/cancel`, {
      reason
    });
    return response.data.data;
  } catch (error) {
    console.error('Error cancelling order:', error);
    throw error;
  }
};

// Fetch order statuses - Return static statuses since API doesn't have this endpoint
export const fetchOrderStatuses = async () => {
  try {
    console.log('📋 Returning static order statuses (API endpoint not available)');
    // Return static statuses based on API documentation
    const statuses = [
      { id: 1, value: 'en_attente', name: 'En attente' },
      { id: 2, value: 'confirmee', name: 'Confirmée' },
      { id: 3, value: 'en_preparation', name: 'En préparation' },
      { id: 4, value: 'expediee', name: 'Expédiée' },
      { id: 5, value: 'livree', name: 'Livrée' },
      { id: 6, value: 'annulee', name: 'Annulée' },
      { id: 7, value: 'remboursee', name: 'Remboursée' },
      { id: 8, value: 'retournee', name: 'Retournée' }
    ];
    return statuses;
  } catch (error) {
    console.error('Error fetching order statuses:', error);
    throw error;
  }
};

// Fetch order history - Return static data since API doesn't have this endpoint
export const fetchOrderHistory = async (orderId) => {
  try {
    console.log('📋 Returning static order history (API endpoint not available)');
    // Return static history data
    const history = [
      {
        id: 1,
        order_id: orderId,
        status: 'en_attente',
        status_label: 'En attente',
        notes: 'Commande créée',
        created_at: new Date().toISOString(),
        user_name: 'Système'
      }
    ];
    return history;
  } catch (error) {
    console.error('Error fetching order history:', error);
    throw error;
  }
};

// Process order payment
export const processOrderPayment = async (orderId, paymentDetails) => {
  try {
    const response = await axiosInstance.post(`/commandes/${orderId}/pay`, {
      payment_details: paymentDetails
    });
    return response.data.data;
  } catch (error) {
    console.error('Error processing payment:', error);
    throw error;
  }
};

// Helper function to convert order from API format to our internal format
function convertOrderFromApi(apiOrder) {
  if (!apiOrder) {
    console.warn('convertOrderFromApi: apiOrder is null or undefined');
    return null;
  }

  // Validate required fields
  if (!apiOrder.id) {
    console.error('convertOrderFromApi: apiOrder.id is missing', apiOrder);
    return null;
  }

  // Additional validation for critical fields
  if (typeof apiOrder.id !== 'number' && typeof apiOrder.id !== 'string') {
    console.error('convertOrderFromApi: apiOrder.id is not a valid type', apiOrder);
    return null;
  }

  console.log('Converting API order:', apiOrder);

  try {
    // Extract customer name from user object first, then fallback to other fields
    const customerName =
      apiOrder.user?.name ||
      apiOrder.nom_client ||
      apiOrder.prenom_client ||
      (apiOrder.email_commande ? apiOrder.email_commande.split('@')[0] : 'Client');

    // Extract shipping address from JSON field or individual fields
    const shippingAddress = apiOrder.shipping_address || {};

    // Safe numeric parsing
    const safeParseFloat = (value, defaultValue = 0) => {
      const parsed = parseFloat(value);
      return isNaN(parsed) ? defaultValue : parsed;
    };

    const safeParseInt = (value, defaultValue = 1) => {
      const parsed = parseInt(value);
      return isNaN(parsed) ? defaultValue : parsed;
    };

    return {
    id: apiOrder.id,
    order_number: apiOrder.numero_commande || `CMD-${String(apiOrder.id).padStart(6, '0')}`,
    customer_id: apiOrder.user_id || apiOrder.client_id,
    customer_name: customerName,
    customer_email: apiOrder.user?.email || apiOrder.email_commande || apiOrder.email || '',
    customer_phone: apiOrder.telephone_commande || apiOrder.telephone || '',

    // Address information
    shipping_name: customerName,
    shipping_address_line1: shippingAddress.street || apiOrder.adresse_commande || apiOrder.adresse || '',
    shipping_city: shippingAddress.city || apiOrder.ville_commande || apiOrder.ville || '',
    shipping_postal_code: shippingAddress.postal_code || apiOrder.code_postal_commande || apiOrder.code_postal || '',
    shipping_country: shippingAddress.country || 'Tunisie',

    // Order totals
    subtotal: parseFloat(apiOrder.subtotal || apiOrder.total_commande || apiOrder.total || 0),
    total: parseFloat(apiOrder.total_commande || apiOrder.total || 0),
    discount: parseFloat(apiOrder.discount_amount || apiOrder.remise_commande || apiOrder.remise || 0),
    shipping_cost: parseFloat(apiOrder.shipping_cost || 0),
    tax: parseFloat(apiOrder.tax_amount || 0),

    // Status information - map according to API documentation
    status: getStatusLabel(apiOrder.status),
    payment_status: apiOrder.payment_status || 'pending',
    payment_method: apiOrder.methode_paiement || 'Carte bancaire',

    // Dates
    created_at: apiOrder.created_at,
    updated_at: apiOrder.updated_at,

    // Items - handle products according to API documentation
    items:
      (apiOrder.produits || []).length > 0
        ? (apiOrder.produits || []).map((product) => ({
            id: product.pivot?.produit_id || product.id,
            product_name: product.nom_produit || product.nom || 'Produit',
            description: product.description || '',
            sku: product.sku || (product.pivot?.produit_id || product.id).toString(),
            unit_price: parseFloat(product.pivot?.prix_unitaire || product.prix || 0),
            quantity: parseInt(product.pivot?.quantite || 1),
            total_price: parseFloat(product.pivot?.total_ligne || (product.pivot?.prix_unitaire || product.prix || 0) * parseInt(product.pivot?.quantite || 1))
          }))
        : [
            {
              id: 1,
              product_name: 'Commande #' + apiOrder.id,
              description: 'Détails non disponibles',
              sku: 'N/A',
              unit_price: parseFloat(apiOrder.total_commande || apiOrder.total || 0),
              quantity: 1,
              total_price: parseFloat(apiOrder.total_commande || apiOrder.total || 0)
            }
          ],

    // Payment information
    payment_info: apiOrder.paiement ? {
      id: apiOrder.paiement.id,
      amount: parseFloat(apiOrder.paiement.montant || 0),
      method: apiOrder.paiement.methode_paiement || apiOrder.methode_paiement,
      status: apiOrder.paiement.status,
      transaction_id: apiOrder.paiement.transaction_id,
      processed_at: apiOrder.paiement.processed_at
    } : null,

    // Additional dates from API
    date_commande: apiOrder.date_commande,
    confirmed_at: apiOrder.confirmed_at,
    preparation_started_at: apiOrder.preparation_started_at,
    shipped_at: apiOrder.shipped_at,
    delivered_at: apiOrder.delivered_at,
    cancelled_at: apiOrder.cancelled_at,
    refunded_at: apiOrder.refunded_at,

    // Notes and promo code
    notes: apiOrder.notes,
    code_promo: apiOrder.code_promo,

    // Original data
    _original: apiOrder
  };
  } catch (error) {
    console.error('Error converting order from API:', error, apiOrder);
    return null;
  }
}

export async function updateOrder(id, data) {
  const apiData = {
    adresse_commande: data.shipping_address_line1,
    ville_commande: data.shipping_city,
    code_postal_commande: data.shipping_postal_code,
    telephone_commande: data.shipping_phone,
    email_commande: data.shipping_email
  };

  const res = await fetch(`${API_URL}/commandes/${id}`, {
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(apiData)
  });
  if (!res.ok) throw new Error('Erreur lors de la mise à jour de la commande');

  const responseData = await res.json();
  return convertOrderFromApi(responseData);
}

export async function createOrder(data) {
  // Create order according to API documentation
  const apiData = {
    cart_id: data.cart_id, // Required: shopping cart ID
    shipping_address: {
      street: data.shipping_address_line1,
      city: data.shipping_city,
      postal_code: data.shipping_postal_code,
      country: data.shipping_country || 'Tunisie'
    },
    billing_address: {
      street: data.billing_address_line1 || data.shipping_address_line1,
      city: data.billing_city || data.shipping_city,
      postal_code: data.billing_postal_code || data.shipping_postal_code,
      country: data.billing_country || data.shipping_country || 'Tunisie'
    },
    methode_paiement: data.payment_method || 'stripe',
    notes: data.notes || '',
    code_promo: data.promo_code || '',
    user_id: data.customer_id,
    client_id: data.client_id,
    guest_details: data.guest_details // For guest checkouts
  };

  const res = await fetch(`${API_URL}/commandes`, {
    method: 'POST',
    headers: getAuthHeaders(),
    body: JSON.stringify(apiData)
  });

  if (!res.ok) {
    const errorText = await res.text();
    console.error('Failed to create order:', {
      status: res.status,
      statusText: res.statusText,
      error: errorText
    });
    throw new Error(`Erreur lors de la création de la commande: ${res.status} - ${errorText}`);
  }

  const response = await res.json();
  return convertOrderFromApi(response.data || response);
}

export async function deleteOrder(id) {
  const res = await fetch(`${API_URL}/commandes/${id}`, {
    method: 'DELETE',
    headers: getAuthHeaders()
  });

  if (!res.ok) {
    const errorText = await res.text();
    console.error('Failed to delete order:', {
      status: res.status,
      statusText: res.statusText,
      error: errorText
    });
    throw new Error(`Erreur lors de la suppression de la commande: ${res.status} - ${errorText}`);
  }

  return res.json();
}

// Client Orders
export async function fetchClientOrders(clientId) {
  const res = await fetch(`${API_URL}/clients/${clientId}/commandes`);
  if (!res.ok) throw new Error('Erreur lors du chargement des commandes du client');

  const data = await res.json();
  return Array.isArray(data) ? data.map(convertOrderFromApi) : [];
}

export async function fetchLastClientOrder(clientId) {
  const res = await fetch(`${API_URL}/clients/${clientId}/derniere-commande`);
  if (!res.ok) throw new Error('Erreur lors du chargement de la dernière commande du client');

  const data = await res.json();
  return convertOrderFromApi(data);
}

// Admin Endpoints
export async function fetchLastClientOrderAdmin(clientId) {
  const res = await fetch(`${API_URL}/v1/admin/clients/${clientId}/derniere-commande`);
  if (!res.ok) throw new Error('Erreur lors du chargement de la dernière commande du client (admin)');

  const data = await res.json();
  return convertOrderFromApi(data);
}
